#!/usr/bin/env python3
"""
Database initialization script for JobSearch application
"""

from app import create_app, db
from app.models import User, SMTPConfig, EmailTemplate, JobSearch

def init_database():
    """Initialize the database with tables"""
    app = create_app()
    
    with app.app_context():
        # Create all tables
        db.create_all()
        print("Database tables created successfully!")
        
        # Create a sample email template for new users
        sample_template = EmailTemplate(
            user_id=1,  # Will be updated when first user registers
            name="Professional Job Application",
            subject="Application for {position} Position at {company_name}",
            body="""Dear {hr_name},

I hope this email finds you well. I am writing to express my strong interest in the {position} position at {company_name}.

With my background and skills, I believe I would be a valuable addition to your team. I am particularly drawn to {company_name} because of your reputation for innovation and excellence in the industry.

I have attached my resume for your review and would welcome the opportunity to discuss how my experience and enthusiasm can contribute to your team's success.

Thank you for considering my application. I look forward to hearing from you soon.

Best regards,
{user_name}
{user_email}""",
            is_default=True
        )
        
        print("Sample data would be created when first user registers.")
        print("Database initialization completed!")

if __name__ == '__main__':
    init_database()
